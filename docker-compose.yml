services:
    # Laravel Octane Application Service
    laravel.app:
        build:
            context: .
            dockerfile: php.Dockerfile
            args:
                - PUID=1000
                - PGID=1000
        container_name: astro-laravel-app
        restart: unless-stopped
        working_dir: /var/www
        volumes:
            - ./:/var/www
        depends_on:
            - mysql
            - redis
        networks:
            - astro-network
        environment:
            - "DB_HOST=mysql"
            - "DB_PORT=3306"
            - "DB_DATABASE=${DB_DATABASE}"
            - "DB_USERNAME=${DB_USERNAME}"
            - "DB_PASSWORD=${DB_PASSWORD}"
            - "REDIS_HOST=redis"
            - "APP_URL=${APP_URL}"
            - "APP_KEY=${APP_KEY}"

    # Nginx Web Server Service
    nginx:
        build:
            context: .
            dockerfile: docker/nginx/Dockerfile
        container_name: astro-nginx
        restart: unless-stopped
        ports:
            - "80:80"
            - "443:443"
        volumes:
            - ./:/var/www
            - ./docker/nginx/default.conf:/etc/nginx/conf.d/default.conf
            # Mount SELURUH direktori certs ke dalam container
            - ./docker/certs:/etc/nginx/ssl:ro
        depends_on:
            - laravel.app
        networks:
            - astro-network

    # MySQL Database Service
    mysql:
        image: mysql:8.0
        container_name: astro-mysql
        restart: unless-stopped
        environment:
            MYSQL_DATABASE: ${DB_DATABASE}
            MYSQL_USER: ${DB_USERNAME}
            MYSQL_PASSWORD: ${DB_PASSWORD}
            MYSQL_ROOT_PASSWORD: ${DB_ROOT_PASSWORD}
        volumes:
            - astro-db-data:/var/lib/mysql
        networks:
            - astro-network
        healthcheck:
            test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
            timeout: 20s
            retries: 10

    # Redis Service
    redis:
        image: redis:alpine
        container_name: astro-redis
        restart: unless-stopped
        networks:
            - astro-network

networks:
    astro-network:
        driver: bridge

volumes:
    astro-db-data:
        driver: local

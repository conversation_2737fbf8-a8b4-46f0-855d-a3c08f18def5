# Version control
.git/
.github/
.gitignore
.gitattributes

# Dependencies
node_modules/
vendor/

# Environment files
.env
.env.local
.env.example
.env.testing

# Development files
tests/
.phpunit.result.cache
phpunit.xml
pest.xml

# Build artifacts
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS files
.DS_Store
Thumbs.db

# Cache and temporary files
storage/logs/*
storage/framework/cache/*
storage/framework/sessions/*
storage/framework/views/*
storage/app/public/*
bootstrap/cache/*
public/hot
public/storage

# Documentation
README.md
CHANGELOG.md
LICENSE
docs/

# Deployment files
deploy.sh
deployment.tar.gz
private_key

# Backup files
*.backup
*.bak

# Compiled assets (will be built on server or included in build)
# public/build/ (comment this out if you want to include built assets)

# Laravel specific
.phpunit.result.cache
.env.backup
.phpstorm.meta.php
_ide_helper.php
_ide_helper_models.php
.phpstorm.meta.php

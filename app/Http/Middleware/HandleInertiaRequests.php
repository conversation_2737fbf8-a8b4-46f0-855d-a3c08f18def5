<?php

namespace App\Http\Middleware;

use App\Models\PaymentSetting;
use Illuminate\Foundation\Inspiring;
use Illuminate\Http\Request;
use Inertia\Middleware;
use Tighten\Ziggy\Ziggy;

class HandleInertiaRequests extends Middleware
{
    /**
     * The root template that's loaded on the first page visit.
     *
     * @see https://inertiajs.com/server-side-setup#root-template
     *
     * @var string
     */
    protected $rootView = 'app';

    /**
     * Determines the current asset version.
     *
     * @see https://inertiajs.com/asset-versioning
     */
    public function version(Request $request): ?string
    {
        return parent::version($request);
    }

    /**
     * Define the props that are shared by default.
     *
     * @see https://inertiajs.com/shared-data
     *
     * @return array<string, mixed>
     */
    public function share(Request $request): array
    {
        [$message, $author] = str(Inspiring::quotes()->random())->explode('-');

        return [
            ...parent::share($request),
            'name' => config('app.name'),
            'quote' => ['message' => trim($message), 'author' => trim($author)],
            'auth' => [
                'user' => $request->user(),
            ],
            'ziggy' => fn (): array => [
                ...(new Ziggy)->toArray(),
                'location' => $request->url(),
            ],
            'sidebarOpen' => ! $request->hasCookie('sidebar_state') || $request->cookie('sidebar_state') === 'true',
            'env' => [
                'WHATSAPP_NUMBER' => PaymentSetting::get('whatsapp_number', env('WHATSAPP_NUMBER')),
                'BANK_NAME' => PaymentSetting::get('bank_name', env('BANK_NAME')),
                'BANK_ACCOUNT_NAME' => PaymentSetting::get('bank_account_name', env('BANK_ACCOUNT_NAME')),
                'BANK_ACCOUNT_NUMBER' => PaymentSetting::get('bank_account_number', env('BANK_ACCOUNT_NUMBER')),
                'WHATSAPP_MESSAGE_TEMPLATE' => PaymentSetting::get('whatsapp_message_template', ''),
            ],
        ];
    }
}

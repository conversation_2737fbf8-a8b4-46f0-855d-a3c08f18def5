# Pingora Configuration for AstroKabinet
version = 1

# Server configuration
[server]
listen = "0.0.0.0:80"
threads = 4

# Upstream configuration (PHP-FPM)
[[upstreams]]
name = "php_backend"
servers = ["127.0.0.1:9000"]
health_check = true

# Virtual host configuration
[[virtual_hosts]]
server_name = ["astrokabinet.id", "www.astrokabinet.id"]
document_root = "/app/public"

# Location blocks
[[virtual_hosts.locations]]
path = "/"
try_files = ["$uri", "$uri/", "/index.php?$query_string"]

[[virtual_hosts.locations]]
path = "~ \\.php$"
fastcgi_pass = "php_backend"
fastcgi_index = "index.php"
fastcgi_param = [
    "SCRIPT_FILENAME $document_root$fastcgi_script_name",
    "QUERY_STRING $query_string",
    "REQUEST_METHOD $request_method",
    "CONTENT_TYPE $content_type",
    "CONTENT_LENGTH $content_length",
    "SCRIPT_NAME $fastcgi_script_name",
    "REQUEST_URI $request_uri",
    "DOCUMENT_URI $document_uri",
    "DOCUMENT_ROOT $document_root",
    "SERVER_PROTOCOL $server_protocol",
    "REQUEST_SCHEME $scheme",
    "HTTPS $https",
    "GATEWAY_INTERFACE CGI/1.1",
    "SERVER_SOFTWARE pingora",
    "REMOTE_ADDR $remote_addr",
    "REMOTE_PORT $remote_port",
    "SERVER_ADDR $server_addr",
    "SERVER_PORT $server_port",
    "SERVER_NAME $server_name",
    "REDIRECT_STATUS 200"
]

# Static file handling
[[virtual_hosts.locations]]
path = "~ \\.(jpg|jpeg|png|gif|ico|css|js|pdf|txt|tar|woff|svg|ttf|eot|woff2)$"
expires = "1y"
add_header = ["Cache-Control: public, immutable"]

# Security
[[virtual_hosts.locations]]
path = "~ /\\."
deny = "all"

[[virtual_hosts.locations]]
path = "~ /\\.env"
deny = "all"

# Gzip compression
[compression]
enabled = true
types = ["text/plain", "text/css", "text/xml", "text/javascript", "application/javascript", "application/json"]
level = 6

# Security headers
[headers]
add = [
    "X-Frame-Options: SAMEORIGIN",
    "X-Content-Type-Options: nosniff",
    "Referrer-Policy: no-referrer-when-downgrade",
    "X-XSS-Protection: 1; mode=block"
]

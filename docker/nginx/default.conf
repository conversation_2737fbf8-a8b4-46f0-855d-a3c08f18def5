# Rate limiting untuk mencegah abuse
limit_req_zone $binary_remote_addr zone=login:10m rate=5r/m;
limit_req_zone $binary_remote_addr zone=api:10m rate=30r/m;
limit_req_zone $binary_remote_addr zone=general:10m rate=60r/m;

# Server block untuk HTTP (Cloudflare akan handle HTTPS)
server {
    listen 80;
    server_name astrokabinet.id www.astrokabinet.id;
    root /var/www/public;

    # Security Headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

    # CSP Header untuk keamanan
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.youtube.com https://youtube.com https://**************; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://fonts.bunny.net; font-src 'self' https://fonts.gstatic.com https://fonts.bunny.net; img-src 'self' data: blob: https:; media-src 'self' blob:; connect-src 'self' https://**************; frame-src 'self' https://www.youtube.com https://youtube.com;" always;

    # Cloudflare Real IP
    set_real_ip_from ************/20;
    set_real_ip_from ************/22;
    set_real_ip_from ************/22;
    set_real_ip_from **********/22;
    set_real_ip_from ************/18;
    set_real_ip_from *************/18;
    set_real_ip_from ************/20;
    set_real_ip_from ************/20;
    set_real_ip_from *************/22;
    set_real_ip_from ************/17;
    set_real_ip_from ***********/15;
    set_real_ip_from **********/13;
    set_real_ip_from **********/14;
    set_real_ip_from **********/13;
    set_real_ip_from **********/22;
    real_ip_header CF-Connecting-IP;

    index index.php;
    charset utf-8;

    # Rate limiting untuk halaman sensitif
    location /login {
        limit_req zone=login burst=3 nodelay;
        try_files $uri $uri/ /index.php?$query_string;
    }

    location /dashboard {
        limit_req zone=api burst=10 nodelay;
        try_files $uri $uri/ /index.php?$query_string;
    }

    # Static files dengan caching optimal
    location ~* \.(jpg|jpeg|png|gif|ico|svg|webp|avif)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Vary "Accept-Encoding";
        access_log off;

        # Image optimization headers
        add_header X-Content-Type-Options "nosniff";

        # Fallback untuk missing images
        try_files $uri $uri/ =404;
    }

    location ~* \.(css|js|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Vary "Accept-Encoding";
        access_log off;
    }

    location ~* \.(pdf|doc|docx|zip|rar)$ {
        expires 30d;
        add_header Cache-Control "public";
        add_header X-Content-Type-Options "nosniff";
        add_header Content-Disposition "attachment";
    }

    # Main location block
    location / {
        limit_req zone=general burst=20 nodelay;
        try_files $uri $uri/ /index.php?$query_string;
    }

    # Special files
    location = /favicon.ico {
        access_log off;
        log_not_found off;
        expires 1y;
    }

    location = /robots.txt {
        access_log off;
        log_not_found off;
        expires 1d;
    }

    # Health check endpoint
    location = /up {
        access_log off;
        return 200 'OK';
        add_header Content-Type text/plain;
    }

    error_page 404 /index.php;

    # PHP-FPM configuration dengan optimasi
    location ~ \.php$ {
        limit_req zone=general burst=20 nodelay;

        fastcgi_pass app:9000;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;

        # Optimasi FastCGI
        fastcgi_buffers 16 16k;
        fastcgi_buffer_size 32k;
        fastcgi_read_timeout 300;
        fastcgi_send_timeout 300;
        fastcgi_connect_timeout 300;

        # Security untuk PHP
        fastcgi_param HTTP_PROXY "";
        fastcgi_param HTTPS on;
        fastcgi_param SERVER_PORT 443;

        include fastcgi_params;
    }

    # Block akses ke file sensitif
    location ~ /\.(?!well-known).* {
        deny all;
        access_log off;
        log_not_found off;
    }

    # Block akses ke file konfigurasi
    location ~* \.(env|log|ini|conf|sql|bak|old|tmp)$ {
        deny all;
        access_log off;
        log_not_found off;
    }

    # Block akses ke direktori sensitif
    location ~ ^/(storage|bootstrap|config|database|resources|tests|vendor)/ {
        deny all;
        access_log off;
        log_not_found off;
    }
}

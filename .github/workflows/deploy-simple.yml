name: Simple Deploy to Production

on:
  push:
    branches:
      - main
  workflow_dispatch:
    inputs:
      force_deploy:
        description: 'Force deploy without tests'
        required: false
        default: 'false'
        type: boolean

env:
  DEPLOY_PATH: /home/<USER>/astrokabinet
  DOCKER_COMPOSE_FILE: docker-compose.production.yml

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: '8.3'
          extensions: dom, curl, libxml, mbstring, zip, pcntl, pdo, sqlite, pdo_sqlite, bcmath, soap, intl, gd, exif, redis
          coverage: none

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'

      - name: Install dependencies and build
        run: |
          echo "Installing Composer dependencies..."
          composer install --no-dev --no-interaction --prefer-dist --optimize-autoloader
          
          echo "Installing NPM dependencies..."
          npm ci || npm install
          
          echo "Building assets..."
          npm run build

      - name: Run tests
        if: github.event.inputs.force_deploy != 'true'
        run: |
          cp .env.example .env
          php artisan key:generate
          php artisan test --parallel

      - name: Create deployment archive
        run: |
          # Create .deployignore
          cat > .deployignore << 'EOF'
          .git/
          .github/
          node_modules/
          tests/
          storage/logs/*.log
          storage/framework/cache/*
          storage/framework/sessions/*
          storage/framework/views/*
          .env
          .env.example
          .gitignore
          .deployignore
          README.md
          *.log
          .DS_Store
          Thumbs.db
          EOF
          
          # Create deployment archive
          tar --exclude-from=.deployignore --exclude='deployment.tar.gz' -czf deployment.tar.gz .
          echo "Archive created: $(ls -lh deployment.tar.gz)"

      - name: Upload to server
        uses: appleboy/scp-action@v0.1.7
        with:
          host: ${{ secrets.VPS_HOST }}
          username: ${{ secrets.VPS_USERNAME }}
          port: ${{ secrets.VPS_PORT }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          source: "deployment.tar.gz"
          target: "/tmp/"
          overwrite: true

      - name: Deploy on server
        uses: appleboy/ssh-action@v1.0.3
        with:
          host: ${{ secrets.VPS_HOST }}
          username: ${{ secrets.VPS_USERNAME }}
          port: ${{ secrets.VPS_PORT }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          script: |
            set -e
            
            echo "=== Starting Deployment ==="
            
            # Create backup
            if [ -d "${{ env.DEPLOY_PATH }}" ]; then
              echo "Creating backup..."
              sudo cp -r ${{ env.DEPLOY_PATH }} ${{ env.DEPLOY_PATH }}.backup.$(date +%Y%m%d-%H%M%S)
              
              # Keep only last 3 backups
              sudo find /home/<USER>/ -name "astrokabinet.backup.*" -type d | sort | head -n -3 | xargs -r sudo rm -rf
            fi
            
            # Stop containers
            echo "Stopping containers..."
            cd ${{ env.DEPLOY_PATH }} && sudo docker-compose -f ${{ env.DOCKER_COMPOSE_FILE }} down || true
            
            # Backup .env
            if [ -f "${{ env.DEPLOY_PATH }}/.env" ]; then
              sudo cp "${{ env.DEPLOY_PATH }}/.env" "/tmp/.env.backup"
            fi
            
            # Extract new deployment
            echo "Extracting deployment..."
            sudo mkdir -p ${{ env.DEPLOY_PATH }}
            sudo tar -xzf /tmp/deployment.tar.gz -C ${{ env.DEPLOY_PATH }}
            
            # Restore .env
            if [ -f "/tmp/.env.backup" ]; then
              sudo cp "/tmp/.env.backup" "${{ env.DEPLOY_PATH }}/.env"
            elif [ ! -f "${{ env.DEPLOY_PATH }}/.env" ]; then
              sudo cp "${{ env.DEPLOY_PATH }}/.env.example" "${{ env.DEPLOY_PATH }}/.env"
            fi
            
            # Set permissions
            sudo chown -R deploy:deploy ${{ env.DEPLOY_PATH }}
            cd ${{ env.DEPLOY_PATH }}
            sudo chmod -R 775 storage bootstrap/cache
            
            # Build and start containers
            echo "Building containers..."
            sudo docker-compose -f ${{ env.DOCKER_COMPOSE_FILE }} build --no-cache
            
            echo "Starting containers..."
            sudo docker-compose -f ${{ env.DOCKER_COMPOSE_FILE }} up -d
            
            # Wait for containers
            echo "Waiting for containers to start..."
            sleep 30
            
            # Health check
            echo "Running health check..."
            for i in {1..10}; do
              if curl -f http://localhost/up > /dev/null 2>&1; then
                echo "✅ Health check passed!"
                break
              elif [ $i -eq 10 ]; then
                echo "❌ Health check failed, rolling back..."
                
                # Rollback
                LATEST_BACKUP=$(sudo find /home/<USER>/ -name "astrokabinet.backup.*" -type d | sort | tail -n 1)
                if [ -n "$LATEST_BACKUP" ]; then
                  sudo docker-compose -f ${{ env.DOCKER_COMPOSE_FILE }} down || true
                  sudo rm -rf ${{ env.DEPLOY_PATH }}
                  sudo mv "$LATEST_BACKUP" ${{ env.DEPLOY_PATH }}
                  cd ${{ env.DEPLOY_PATH }}
                  sudo docker-compose -f ${{ env.DOCKER_COMPOSE_FILE }} up -d
                  echo "Rolled back to previous version"
                fi
                exit 1
              fi
              echo "Health check attempt $i/10..."
              sleep 10
            done
            
            # Laravel optimizations
            echo "Running Laravel optimizations..."
            sudo docker-compose -f ${{ env.DOCKER_COMPOSE_FILE }} exec -T app php artisan config:cache || true
            sudo docker-compose -f ${{ env.DOCKER_COMPOSE_FILE }} exec -T app php artisan route:cache || true
            sudo docker-compose -f ${{ env.DOCKER_COMPOSE_FILE }} exec -T app php artisan view:cache || true
            
            # Cleanup
            sudo rm -f /tmp/deployment.tar.gz /tmp/.env.backup
            
            echo "✅ Deployment completed successfully!"

      - name: Notify result
        if: always()
        run: |
          if [ "${{ job.status }}" == "success" ]; then
            echo "✅ Deployment successful!"
          else
            echo "❌ Deployment failed!"
          fi

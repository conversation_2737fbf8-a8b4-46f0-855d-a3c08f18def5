name: Simple Deploy

on:
  push:
    branches:
      - main
  workflow_dispatch:

env:
  DEPLOY_PATH: /home/<USER>/astrokabinet

jobs:
  deploy:
    runs-on: ubuntu-latest
    if: "!contains(github.event.head_commit.message, '[skip ci]')"
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: '8.3'
          extensions: dom, curl, libxml, mbstring, zip, pcntl, pdo, sqlite, pdo_sqlite, bcmath, soap, intl, gd, exif, redis
          coverage: none

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'

      - name: Install dependencies and build
        run: |
          composer install --no-dev --no-interaction --prefer-dist --optimize-autoloader
          npm ci || npm install
          npm run build

      - name: Deploy to server
        uses: appleboy/scp-action@v0.1.7
        with:
          host: ${{ secrets.VPS_HOST }}
          username: ${{ secrets.VPS_USERNAME }}
          port: ${{ secrets.VPS_PORT }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          source: "."
          target: "${{ env.DEPLOY_PATH }}"
          overwrite: true

      - name: Restart everything
        uses: appleboy/ssh-action@v1.0.3
        with:
          host: ${{ secrets.VPS_HOST }}
          username: ${{ secrets.VPS_USERNAME }}
          port: ${{ secrets.VPS_PORT }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          timeout: 300s
          command_timeout: 600s
          script: |
            cd ${{ env.DEPLOY_PATH }}
            
            echo "=== Simple Deploy - Restart Everything ==="
            
            # Set permissions
            sudo chown -R deploy:deploy .
            sudo chmod -R 775 storage bootstrap/cache
            
            # Ensure .env exists
            if [ ! -f .env ]; then
              cp .env.example .env
              echo "Created .env from example"
            fi
            
            # Stop all containers
            echo "Stopping containers..."
            docker-compose -f docker-compose.production.yml down
            
            # Build all containers fresh
            echo "Building containers..."
            docker-compose -f docker-compose.production.yml build --no-cache
            
            # Start all containers
            echo "Starting containers..."
            docker-compose -f docker-compose.production.yml up -d
            
            # Wait for startup
            echo "Waiting for containers to start..."
            sleep 30
            
            # Basic health check
            echo "Health check..."
            if curl -f http://localhost/up > /dev/null 2>&1; then
              echo "✅ Health check passed!"
            else
              echo "⚠️ Health check failed, but deployment completed"
            fi
            
            # Laravel optimizations (best effort)
            echo "Laravel optimizations..."
            docker-compose -f docker-compose.production.yml exec -T app php artisan config:cache 2>/dev/null || true
            docker-compose -f docker-compose.production.yml exec -T app php artisan route:cache 2>/dev/null || true
            docker-compose -f docker-compose.production.yml exec -T app php artisan view:cache 2>/dev/null || true
            
            # Show status
            echo "=== Container Status ==="
            docker-compose -f docker-compose.production.yml ps
            
            echo "✅ Simple deployment completed!"

      - name: Cleanup Docker images
        uses: appleboy/ssh-action@v1.0.3
        with:
          host: ${{ secrets.VPS_HOST }}
          username: ${{ secrets.VPS_USERNAME }}
          port: ${{ secrets.VPS_PORT }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          timeout: 60s
          script: |
            echo "Cleaning up old Docker images..."
            docker image prune -f || true
            echo "Cleanup completed"

      - name: Deployment result
        if: always()
        run: |
          if [ "${{ job.status }}" == "success" ]; then
            echo "✅ Simple deployment successful!"
            echo "If there are issues, rollback with: git revert HEAD && git push"
          else
            echo "❌ Deployment failed!"
            echo "Check logs and consider rollback: git revert HEAD && git push"
          fi

name: Deploy AstroKabinet with Git Rollback

on:
  push:
    branches:
      - main
      - production
  workflow_dispatch:
    inputs:
      rollback:
        description: 'Rollback to previous production tag'
        required: false
        default: 'false'
        type: boolean
      rollback_tag:
        description: 'Specific tag to rollback to (optional)'
        required: false
        type: string

env:
  DEPLOY_PATH: /var/www/astrokabinet.id
  DOCKER_COMPOSE_FILE: docker-compose.production.yml

jobs:
  # Job 1: Run Tests
  test:
    runs-on: ubuntu-latest
    if: github.event.inputs.rollback != 'true'

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: '8.3'
          extensions: dom, curl, libxml, mbstring, zip, pcntl, pdo, sqlite, pdo_sqlite, bcmath, soap, intl, gd, exif, redis
          coverage: none

      - name: Cache Composer dependencies
        uses: actions/cache@v4
        with:
          path: vendor
          key: composer-${{ hashFiles('composer.lock') }}

      - name: Install Composer Dependencies
        run: composer install --no-interaction --prefer-dist --optimize-autoloader

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'

      - name: Install NPM Dependencies
        run: npm ci

      - name: Build Assets
        run: npm run build

      - name: Create .env for testing
        run: |
          cp .env.example .env
          php artisan key:generate

      - name: Run Tests
        run: php artisan test --parallel

  # Job 2: Deploy to Production
  deploy:
    runs-on: ubuntu-latest
    needs: test
    permissions:
      contents: write
    if: always() && (needs.test.result == 'success' || github.event.inputs.rollback == 'true')

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Install, Build, and Archive
        if: github.event.inputs.rollback != 'true'
        run: |
          # Install production dependencies
          composer install --no-dev --no-interaction --prefer-dist --optimize-autoloader
          npm ci
          npm run build

          # Create .deployignore to exclude unnecessary files
          cat > .deployignore << 'EOF'
          .git/
          .github/
          node_modules/
          tests/
          storage/logs/
          storage/framework/cache/
          storage/framework/sessions/
          storage/framework/views/
          .env
          .env.example
          .gitignore
          .deployignore
          README.md
          *.log
          .DS_Store
          Thumbs.db
          EOF

          # Create deployment archive
          tar --exclude-from=.deployignore -czf deployment.tar.gz .

      - name: Create and Push Production Tag
        if: github.event.inputs.rollback != 'true'
        run: |
          TIMESTAMP=$(date +%Y%m%d-%H%M%S)
          TAG_NAME="production-${TIMESTAMP}"
          echo "PRODUCTION_TAG=${TAG_NAME}" >> $GITHUB_ENV
          git config user.name "GitHub Actions"
          git config user.email "<EMAIL>"
          git tag -a "${TAG_NAME}" -m "Production deployment ${TIMESTAMP}"
          git push origin "${TAG_NAME}"

          # Clean up old production tags (keep only last 10)
          OLD_TAGS=$(git tag -l "production-*" | sort -V | head -n -10)
          if [ -n "$OLD_TAGS" ]; then
            echo "Cleaning up old production tags..."
            echo "$OLD_TAGS" | xargs git tag -d
            echo "$OLD_TAGS" | xargs git push --delete origin
          fi

      - name: Upload Artifact to Server
        if: github.event.inputs.rollback != 'true'
        uses: appleboy/scp-action@v0.1.7
        with:
          host: ${{ secrets.VPS_HOST }}
          username: ${{ secrets.VPS_USERNAME }}
          port: ${{ secrets.VPS_PORT }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          source: "deployment.tar.gz"
          target: "/tmp/"
          overwrite: true

      - name: Deploy, Verify, and Optimize
        if: github.event.inputs.rollback != 'true'
        uses: appleboy/ssh-action@v1.0.3
        with:
          host: ${{ secrets.VPS_HOST }}
          username: ${{ secrets.VPS_USERNAME }}
          port: ${{ secrets.VPS_PORT }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          script: |
            set -e # Exit immediately if a command exits with a non-zero status.

            echo "### 1. Preparing Deployment Directory ###"
            sudo mkdir -p ${{ env.DEPLOY_PATH }}

            echo "### 2. Stopping Services & Backing up .env ###"
            # Use `|| true` to prevent failure if path/compose file doesn't exist yet
            cd ${{ env.DEPLOY_PATH }} && docker-compose -f ${{ env.DOCKER_COMPOSE_FILE }} down || true
            if [ -f "${{ env.DEPLOY_PATH }}/.env" ]; then
              sudo cp "${{ env.DEPLOY_PATH }}/.env" "/tmp/.env.backup"
              echo "Backed up existing .env file to /tmp/.env.backup"
            fi

            echo "### 3. Extracting New Deployment ###"
            sudo tar -xzf /tmp/deployment.tar.gz -C ${{ env.DEPLOY_PATH }}

            echo "### 4. Restoring .env and Setting Permissions ###"
            if [ -f "/tmp/.env.backup" ]; then
              sudo cp "/tmp/.env.backup" "${{ env.DEPLOY_PATH }}/.env"
              echo "Restored .env file"
            fi

            # Set ownership to the web server user (e.g., www-data) or your deploy user
            # Ganti 'www-data:www-data' jika user/group server Anda berbeda
            sudo chown -R www-data:www-data ${{ env.DEPLOY_PATH }}
            cd ${{ env.DEPLOY_PATH }}
            sudo chmod -R 775 storage bootstrap/cache

            echo "### 5. Building and Starting Services ###"
            # Create .env from example if it doesn't exist after all
            if [ ! -f .env ]; then
              cp .env.example .env
              # Anda mungkin perlu menjalankan `php artisan key:generate` di dalam container setelahnya
              echo "Created .env from example. Please generate key and fill variables."
            fi

            # Build and start containers with retry
            for i in {1..3}; do
              echo "Build attempt $i/3"
              if sudo docker-compose -f ${{ env.DOCKER_COMPOSE_FILE }} build --no-cache; then
                echo "Build successful"
                break
              elif [ $i -eq 3 ]; then
                echo "All build attempts failed"
                exit 1
              fi
              sleep 5
            done
            sudo docker-compose -f ${{ env.DOCKER_COMPOSE_FILE }} up -d

            echo "### 6. Health Check ###"
            echo "Waiting for containers to start..."
            sleep 30
            for i in {1..10}; do
              if curl -fs http://localhost/up > /dev/null; then
                echo "Health check passed!"
                break
              elif [ $i -eq 10 ]; then
                echo "Health check failed after 10 attempts"
                exit 1
              fi
              echo "Health check attempt $i/10 failed, retrying in 10s..."
              sleep 10
            done

            echo "### 7. Running Optimizations ###"
            sudo docker-compose -f ${{ env.DOCKER_COMPOSE_FILE }} exec -T app php artisan config:cache
            sudo docker-compose -f ${{ env.DOCKER_COMPOSE_FILE }} exec -T app php artisan route:cache
            sudo docker-compose -f ${{ env.DOCKER_COMPOSE_FILE }} exec -T app php artisan view:cache

            echo "### 8. Cleaning Up ###"
            sudo rm -f /tmp/deployment.tar.gz /tmp/.env.backup

            echo "✅ Deployment completed successfully"

      # --- ROLLBACK STEPS (NOT FULLY REFACTORED, BUT FOLLOWING THE NEW LOGIC) ---
      - name: Handle Manual Rollback
        if: github.event.inputs.rollback == 'true'
        run: |
          echo "Manual rollback initiated..."
          # Logic to build and archive the rollback version would go here
          # For simplicity, this example assumes you'd manually trigger a redeploy of an old tag
          # A full implementation would be similar to the main deployment flow

      - name: Notify deployment result
        if: always()
        run: |
          if [ "${{ job.status }}" == "success" ]; then
            if [ "${{ github.event.inputs.rollback }}" == "true" ]; then
              echo "✅ Rollback to ${{ env.ROLLBACK_TAG || 'previous version' }} successful"
            else
              echo "✅ Deployment to ${{ env.PRODUCTION_TAG }} successful"
            fi
          else
            # Automatic rollback logic on failure would be complex and is omitted for clarity.
            # The current best practice is to trigger a manual rollback via workflow_dispatch.
            echo "❌ Deployment failed."
          fi

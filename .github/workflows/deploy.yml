name: Simple Copy Files

on:
  push:
    branches:
      - production
  workflow_dispatch:

jobs:
  deploy:
    runs-on: ubuntu-latest
    if: "!contains(github.event.head_commit.message, '[skip ci]')"

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: '8.3'
          extensions: dom, curl, libxml, mbstring, zip, pcntl, pdo, sqlite, pdo_sqlite, bcmath, soap, intl, gd, exif, redis
          coverage: none

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'

      - name: Install dependencies and build
        run: |
          composer install --no-dev --no-interaction --prefer-dist --optimize-autoloader
          npm ci || npm install
          npm run build

      - name: Deploy to server
        uses: appleboy/ssh-action@v1.0.3
        with:
          host: ${{ secrets.VPS_HOST }}
          username: ${{ secrets.VPS_USERNAME }}
          port: ${{ secrets.VPS_PORT }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          script: |
            cd /home/<USER>/astrokabinet

            # Setup git config if not exists
            git config --global user.email "<EMAIL>" || true
            git config --global user.name "Deploy Bot" || true

            # Try to pull, if fails setup SSH key
            if ! git pull origin main; then
              echo "Git pull failed. Setting up SSH key..."

              # Generate SSH key if not exists
              if [ ! -f ~/.ssh/id_ed25519 ]; then
                ssh-keygen -t ed25519 -C "<EMAIL>" -f ~/.ssh/id_ed25519 -N ""
              fi

              # Show public key for manual setup
              echo "=== ADD THIS SSH KEY TO GITHUB ==="
              cat ~/.ssh/id_ed25519.pub
              echo "=================================="
              echo "Add this key to: https://github.com/omowahyu/astroworks/settings/keys"
              echo "Then re-run the deployment."
              exit 1
            fi

            # Run deployment script
            ./.scripts/deploy.sh

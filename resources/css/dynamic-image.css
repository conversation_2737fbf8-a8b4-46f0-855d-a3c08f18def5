/* Dynamic Image Components - Optimized for seamless mobile/desktop transitions */

.dynamic-image-single {
  position: relative;
  overflow: hidden;
  background-color: #f3f4f6;
}

/* Critical: Responsive aspect ratios for seamless transitions */
@media (max-width: 767px) {
  .dynamic-image-single {
    aspect-ratio: 4 / 5;
  }
}

@media (min-width: 768px) {
  .dynamic-image-single {
    aspect-ratio: 16 / 9;
  }
}

/* Optimize images for smooth transitions */
.dynamic-image-single img {
  transform: translateZ(0); /* Force GPU acceleration */
  backface-visibility: hidden;
  will-change: opacity;
  transition: opacity 0.3s ease-out;
}

/* Ensure proper hiding/showing with Tailwind classes */
.dynamic-image-single .mobile-image {
  display: block;
}

@media (min-width: 768px) {
  .dynamic-image-single .mobile-image {
    display: none !important;
  }
}

.dynamic-image-single .desktop-image {
  display: none;
}

@media (min-width: 768px) {
  .dynamic-image-single .desktop-image {
    display: block !important;
  }
}

/* Gallery specific styles */
.dynamic-gallery-container {
  position: relative;
  overflow: hidden;
}

@media (max-width: 767px) {
  .dynamic-gallery-container {
    aspect-ratio: 4 / 5;
  }
}

@media (min-width: 768px) {
  .dynamic-gallery-container {
    aspect-ratio: 16 / 9;
  }
}

/* Smooth transitions for touch devices */
@media (hover: none) {
  .group:hover .opacity-0 {
    opacity: 1;
  }
}

/* Hide scrollbar for thumbnail strip */
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* Loading states */
.image-loading {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Performance optimizations */
.dynamic-image-single,
.dynamic-gallery-container {
  contain: layout style paint;
}

/* Preload optimization */
.dynamic-image-single img[loading="lazy"] {
  content-visibility: auto;
}
